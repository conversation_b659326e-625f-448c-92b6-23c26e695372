#!/usr/bin/env python
# coding: utf-8

# API Endpoint file

# To run as server on linux
# uvicorn main:app --port 5000 --reload

# To run as server on windows
# python -m uvicorn main:app --port 5000 --reload

import os
import json
import socket
import uvicorn                                              # type: ignore
from urls.v2_urls import *
from classes import *
from constants import *
from pathlib import Path
from controllers import *
from autoDataServices.V2_autoDataServices import *
from autoDataServices.V1_autoDataServices import *
from fastapi.staticfiles import StaticFiles                 # type: ignore
from fastapi.encoders import jsonable_encoder               # type: ignore
from fastapi import FastAPI, HTTPException, Response        # type: ignore
from fastapi.responses import JSONResponse, FileResponse    # type: ignore
from fastapi.middleware.cors import CORSMiddleware          # type: ignore

app = FastAPI(
    title="Carmy Server",
    summary="Python Server for the Carmy Application. Contains all API calls related to AUTODATA"
)

if __name__ == '__main__':
    hostname = socket.gethostname()
    ipAddress = socket.gethostbyname(hostname)
    ipv6Addr = socket.getaddrinfo(hostname, api_port, socket.AF_INET6)[0][4][0]
    print("IPV6 test : ",ipv6Addr)
    uvicorn.run('endpoint:app', port=api_port, reload=reloadFlag, workers=worker_count, host=ipAddress)
    print(f"\nTo view the Swagger page, please follow this link \nhttps://{ipAddress}/docs\n")
    # uvicorn.run('endpoint:app', port=api_port, reload=reloadFlag, workers=worker_count)

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=methods,
    allow_headers=headers,
)

# Test Runs for functions

@app.get('/')
async def default_return():
    print("Carmy Test Page Invoked....")
    return "Carmy Test Page"
    # return RedirectResponse(url="login.html")

@app.get("/favicon.ico", response_class=FileResponse)
async def get_icon():
    image_path = os.path.join(assetImages, 'favicon.ico')
    if os.path.isfile(image_path):
        return FileResponse(image_path)
    else:
        raise HTTPException(status_code=404, detail="Image not found")

# Endpoint to get asset images

@app.get('/images/{imageName}', response_class=FileResponse)
async def getAssetImage(imageName: str):
    image_path = os.path.join(assetImages, imageName)
    if os.path.isfile(image_path):
        return FileResponse(image_path)
    else:
        raise HTTPException(status_code=404, detail="Image not found")

# Endpoints for Back-end Api Calls

@app.post('/get-plate-details/plateNumber={plateNumber}&type={callType}')
async def get_plate_details(callType: str, plateNumber: str, input: PlateDetailRequest = PlateDetailRequest()):
    '''
    Custom Error Returns
    - `error`: *Message indicating Error Type*\n
    Returns Data of the format {
    - `plate`: *Number Plate of the car* [Not Available for Ireland]
    - `vehicles`: List of Vehicle Details as json objects.
    - `type`: *vrm* or *other* based on what type of plate number is. \n
    Available Keys for each vehicle object :-\n
    - *manufacturer* : String
    - *manufacturer_id* : String
    - *model_id* : String
    - *model* : String
    - *subbody* : String
    - *litres* : Decimal
    - *fuel* : String
    - *extra_info* : String
    - *enginecode* : String
    - *kw* : Number
    - *tuning* : String
    - *rpm* : Number
    - *din_hp* : Number
    - *start_year* : String
    - *end_year* : String
    - *mid* : String
    - *veh_type* : String
    - *type_abbr* : String
    - *sort_order* : Number
    - *href* : String
    - *warnings* : String
    - *technical_info* : String\n
    }\n    
    '''
    input.countryCode = input.countryCode.strip().lower()
    otherCountryParams = {
        'du': {
            'method': 'kba', 
            'code': 'gb'
        },
        'ch': {
            'method': 'typesch', 
            'code': 'ch'
        }
    }
    country_code = ''
    print(f"Get Plate Details Query Params : Plate Number ({plateNumber}) : Call Type ({callType})")
    print(f"Get Plate Details Request Params : {input.getJson()}")
    if(input.countryCode==''):
        country_code = getCountryFromIp(ipAddress=input.ipAddress)
    else:
        country_code = input.countryCode
    if(country_code=='error'):
        plateDetails = {'error' : 'Unable to validate IP Address'}
        return json.dumps(plateDetails)
    elif(country_code=='se'):
        plateDetails = getVehicleVrmSearchCall(
            plateNumber=plateNumber, 
            method=callType,
            limit=input.limit, 
            page=input.page, 
            countryCode='se'
            )
    else:
        plateDetails = getVehicleByOtherCountryId(
            vehicle_id=plateNumber, 
            method=otherCountryParams[country_code]['method'], 
            countryCode=otherCountryParams[country_code]['code'], 
            page=input.page, 
            limit=input.limit
            )
    return json.dumps(plateDetails)

@app.post('/get-mid/method={method}&id={given_id}')
async def get_mid(method: str, given_id: str, input: PlateDetailRequest = PlateDetailRequest()):
    '''
    Allowed Methods : k_type, engine_code\n
    Custom Error Returns
    - `error`: *Message indicating Error Type*\n
    Returns Data of the format {
    - `vehicles`: List of Vehicle Details as json objects. \n
    Available Keys for each vehicle object :-\n
    - *manufacturer* : String
    - *manufacturer_id* : String
    - *model_id* : String
    - *model* : String
    - *subbody* : String
    - *litres* : Decimal
    - *fuel* : String
    - *extra_info* : String
    - *enginecode* : String
    - *kw* : Number
    - *tuning* : String
    - *rpm* : Number
    - *din_hp* : Number
    - *start_year* : String
    - *end_year* : String
    - *mid* : String
    - *veh_type* : String
    - *type_abbr* : String
    - *sort_order* : Number
    - *href* : String
    - *warnings* : String
    - *technical_info* : String\n
    }\n    
    '''
    input.countryCode = input.countryCode.strip().lower()
    if(method=='k_type'):
        response = retrieve_MID_by_K_Type(k_type=given_id, method=method, countryCode='gb', page=input.page, limit=input.limit)
        return json.dumps(response)
    elif(method=='engine_code'):
        response = retrieve_MID_by_Engine_Code(engine_code=given_id, method=method, country_code='gb', page=input.page, limit=input.limit)
        return json.dumps(response)
    else:
        return json.dumps({'error': 'Invalid Method Invoked....'})
    
@app.post('/get_ktype/m_id={m_id}')
async def get_k_type(m_id: str, input: PlateDetailRequest = PlateDetailRequest()):
    '''
    Custom Error Returns
    - `error`: *Message indicating Error Type*\n
    Returns Data of the format {
    - `k_types`: List of Vehicle IDs as json objects. \n
    Available Keys for each vehicle object :-\n
    - *id* : Number\n
    }\n    
    '''
    input.countryCode = input.countryCode.strip().lower()
    response = retrieve_K_Type_by_MID(mid=m_id, method='k_type', countryCode='gb', page=input.page, limit=input.limit)
    return json.dumps(response)

@app.post('/get-chasis-details/{chasis_id}')
async def get_details_by_audatex(chasis_id: str, input: PlateDetailRequest = PlateDetailRequest()):
    '''
    Allowed Methods : k_type, engine_code\n
    Custom Error Returns
    - `error`: *Message indicating Error Type*\n
    Returns Data of the format {
    - `vehicles`: List of Vehicle Details as json objects. \n
    Available Keys for each vehicle object :-\n
    - *manufacturer* : String
    - *manufacturer_id* : String
    - *model_id* : String
    - *model* : String
    - *subbody* : String
    - *litres* : Decimal
    - *fuel* : String
    - *extra_info* : String
    - *enginecode* : String
    - *kw* : Number
    - *tuning* : String
    - *rpm* : Number
    - *din_hp* : Number
    - *start_year* : String
    - *end_year* : String
    - *mid* : String
    - *veh_type* : String
    - *type_abbr* : String
    - *sort_order* : Number
    - *href* : String
    - *warnings* : String
    - *technical_info* : String\n
    }\n    
    '''
    input.countryCode = input.countryCode.strip().lower()
    plateDetails = retrieve_Vehicle_from_Other_Country(vehicle_id=chasis_id, type='other', method='audx', countryCode='gb', page=input.page, limit=input.limit)
    return json.dumps(plateDetails)

# V1 API Calls

## Vehicle Selection

@app.post('/search-manufacturer')
async def get_mfd_details(input:PlateDetailRequest = PlateDetailRequest()):
    '''
    Custom Error Returns
    - `error`: *Message indicating Error Type*\n
    '''
    input.countryCode = input.countryCode.strip().lower()
    print(f'Search Manufacturer by Country request : {input.getJson()}')
    if(input.countryCode==''):
        input.countryCode = getCountryFromIp(ipAddress=input.ipAddress)

    if(input.countryCode=='error'):
        plateDetails = {'error' : 'Unable to validate IP Address'}
        return json.dumps(plateDetails)
    else:
        pagespecs = PageSpecs()
        pagespecs.limit = input.limit
        pagespecs.page = input.page
        plateDetails = searchMfdByCountry(
            countryCode=input.countryCode, 
            pageSpecs=pagespecs)
        return json.dumps(plateDetails)
    
@app.post('/search-model-by-mfd/{mfd_id}')
async def get_model_by_mfd(mfd_id:str, input: PlateDetailRequest = PlateDetailRequest()):
    '''
    Custom Error Returns\n
    - `error`: *Message indicating Error Type*\n
    Inputs\n
    - `mfd_id` : Id of the Manufacturer
    '''
    input.countryCode = input.countryCode.strip().lower()
    print(f'Search Model by Manufacturer request : {input.getJson()}')
    if(input.countryCode==''):
        input.countryCode = getCountryFromIp(ipAddress=input.ipAddress)

    if(input.countryCode=='error'):
        plateDetails = {'error' : 'Unable to validate IP Address'}
        return json.dumps(plateDetails)
    else:
        plateDetails = searchModelsByMfd(mfdId=mfd_id, countryCode=input.countryCode)
        return json.dumps(plateDetails)

@app.post('/get-vehicle-by-mfd-and-model/mfd={mfd_id}/model={model_id}')
async def get_vehicle_by_mfd_and_model(mfd_id:str, model_id:str, input:PlateDetailRequest = PlateDetailRequest()):
    '''
    Custom Error Returns\n
    - `error`: *Message indicating Error Type*\n
    Inputs\n
    - `mfd_id` : *Id of the Manufacturer*\n
    - `model_id` : *Id of the Model*
    '''
    input.countryCode = input.countryCode.strip().lower()
    print(f'Search Vehicle by Manufacturer and Model request : {input.getJson()}')
    if(input.countryCode==''):
        input.countryCode = getCountryFromIp(ipAddress=input.ipAddress)

    if(input.countryCode=='error'):
        plateDetails = {'error' : 'Unable to validate IP Address'}
        return json.dumps(plateDetails)
    else:
        pageSpecs = PageSpecs()
        pageSpecs.limit = input.limit
        pageSpecs.page = input.page
        plateDetails = getVehicleByMfdAndModel(mfdId=mfd_id, modelId=model_id, countryCode=input.countryCode, pageSpecs=pageSpecs)
        return json.dumps(plateDetails)

@app.post('/get-vehicle-by-mid/mid={mid}/links={links}')
async def get_vehicle_by_mid(mid:str, links:str, input:PlateDetailRequest = PlateDetailRequest()):
    '''
    Custom Error Returns\n
    - `error`: *Message indicating Error Type*\n
    Inputs\n
    - `mid` : *Id of the Vehicle*\n
    - `links` : *Yes* or *No*\n
    '''
    input.countryCode = input.countryCode.strip().lower()
    print(f'Search Vehicle by Manufacturing ID request : {input.getJson()}')
    if(input.countryCode==''):
        input.countryCode = getCountryFromIp(ipAddress=input.ipAddress)

    if(input.countryCode=='error'):
        plateDetails = {'error' : 'Unable to validate IP Address'}
        return json.dumps(plateDetails)
    else:
        plateDetails = getVehicleMyMid(mid=mid, countryCode=input.countryCode, links=links)
        return json.dumps(plateDetails)

## Service Schedules

@app.post('/search-service-schedule-id/{mid}')
async def search_service_schedule_id(mid:str, input:PlateDetailRequest = PlateDetailRequest()):
    input.countryCode = input.countryCode.strip().lower()
    print(f'Search Service Schedule ID request : {input.getJson()}')
    if(input.countryCode==''):
        input.countryCode = getCountryFromIp(ipAddress=input.ipAddress)

    if(input.countryCode=='error'):
        plateDetails = {'error' : 'Unable to validate IP Address'}
        return json.dumps(plateDetails)
    else:
        plateDetails = searchServiceScheduleId(mid=mid, countryCode=input.countryCode)
        return json.dumps(plateDetails)

## Lifetime Summary

## Service Advisor

## Common Service Operations

## Service Indicators

## Service Brakes

## Engine Oil

## Transmissions

## Operation Lifetime Summary

## Key Operation Lifetime Summary

## AC Recharge

## Engine Management

## Camshaft Drive Replacements

## Auxiliary Drive Belts

## Clutch Replacements

## Degat Procedures

## ABS

## Wheel Alignment Data

## Wheel Alignment Data Notes

## Tyre Pressure

## Electronic Parking Brakes

## TPMS

## Tyres

## SRS

## Key Programming Procedures

## Battery Replacements

## Wiring Diagrams

## Diagnostic Trouble Codes

## Electrical Components Locations

## Bulb Replacements

## Injection and Ignition

## Tuning and Emissions

## Emissions

## Engine Service Operations

## Lubricants and Fluids

## Tightening Torques

## Starting and Charging

## Brake Disc and Drum Dimensions

## Repair Times

## Repair Times by Genartnr

## Repair Times by Ad Part

## Repair Times by ID Voce

## Repair Times for all Vehicle Variants

## Known Problem Areas

## Fixes and Bulletins

## Notes

## Tools

## Components Locations

## Jacking Procedures

## Glass Cars

## Illustrations

## Images

## Operation Types ID

## Operation ID

## Symbols

## PIN Data