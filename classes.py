from pydantic import BaseModel

class PlateDetailRequest(BaseModel):
    page: int = 1
    limit: int = 20
    countryCode: str = ''
    ipAddress: str = '0.0.0.0'

    def getJson(self):
        jsonValue = {
            'page': self.page,
            'limit': self.limit,
            'countryCode': self.countryCode,
            'ipAddress': self.ipAddress
        }
        return jsonValue
    
class PageSpecs(BaseModel):
    page:int = 1
    limit:int = 10

    def getJson(self):
        jsonValue = {
            'page': self.page,
            'limit': self.limit
        }
        return jsonValue