import json
import requests
from urls.v2_urls import *
from constants import *
from controllers import *

# Vehicle VRM Search

def getVehicleVrmSearchCall(plateNumber: str, method: str, page: int = 1, limit: int = 20, countryCode: str = 'se'):
    '''
    Returns Data of the format {
    - `plate`: *Number Plate of the car* [Not Available for Ireland]
    - `vehicles`: List of Vehicle Details as dicts. \n
    Available Keys :-\n
    - *manufacturer*
    - *manufacturer_id*
    - *model_id*
    - *model*
    - *subbody*
    - *litres*
    - *fuel*
    - *extra_info*
    - *enginecode*
    - *kw*
    - *tuning*
    - *rpm*
    - *din_hp*
    - *start_year*
    - *end_year*
    - *mid*
    - *veh_type*
    - *type_abbr*
    - *sort_order*
    - *href*
    - *warnings*
    - *technical_info*\n
    }\n
    Expected Parameters :- \n 
    - `plateNumber`: Vehicle's Registration Number (String)
    - `page`: n'th page to view in cases of long list of details (int)
    - `limit`: Number of entries to show in one page (int)
    - `countryCode`: Two letters symbolizing country of registration (String)
    '''
    url = getVehicleVrmSearchUrl(vrmId=plateNumber, callType=method, countryCode=countryCode, page=page, limit=limit)
    print(f"VRM Fetching url : {url}")

    headers = {
        'Accept-Language': getVrmAcceptLanguage(countryCode),
        'Accept': accept,
        'X-Originating-IP': xOriginatingIp
    }

    returnVal = {
        'error' : 'Test Error'
    }

    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                if(countryCode=='ir'):
                    returnVal = {'vehicles': responseData['data']}
                else:
                    returnVal = response.json()
        else:
            print(f"VRM fetching error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"VRM fetching error details : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

# Vehicle Third Party Identifier Search

def retrieve_MID_by_K_Type(k_type: str, method: str, countryCode: str = 'se', page: int = 1, limit: int = 20):
    url = getMidByKtype(Ktype=k_type, method=method, countryCode=countryCode, page=page, limit=limit)
    print(f"Retrieve MID by K-Type Fetching url : {url}")

    headers = {
        'Accept-Language': getVrmAcceptLanguage(countryCode),
        'Accept': accept,
        'X-Originating-IP': xOriginatingIp
    }

    returnVal = {
        'error' : 'Test Error'
    }

    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Retrieve MID by K-Type Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'vehicles': responseData['data']}
        else:
            print(f"Retrieve MID by K-Type fetching error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Retrieve MID by K-Type fetching error details : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def retrieve_K_Type_by_MID(mid: str, method: str = 'k_type', countryCode: str = 'se', page: int = 1, limit: int = 20):
    url = getKtypeByMid(mid=mid, method=method, countryCode=countryCode, page=page, limit=limit)
    print(f"Retrieve K-Type by MID Fetching url : {url}")

    headers = {
        'Accept-Language': getVrmAcceptLanguage(countryCode),
        'Accept': accept,
        'X-Originating-IP': xOriginatingIp
    }

    returnVal = {
        'error' : 'Test Error'
    }

    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Retrieve K-Type by MID Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'k_types': responseData['data']}
        else:
            print(f"Retrieve K-Type by MID fetching error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Retrieve K-Type by MID fetching error details : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def retrieve_Vehicle_from_Other_Country(vehicle_id: str, type: str, method: str, countryCode: str = 'se', page: int = 1, limit: int = 20):
    # Methods : {germany : kba, swiss : typsch, audatex : audx}
    # Type : {vrm, other}
    url = getVehicleByOtherCountryId(vehicle_id=vehicle_id, method=method, countryCode=countryCode, page=page, limit=limit)
    print(f"Retrieve {countryCodes[countryCode]} Vehicle Details url : {url}")

    headers = {
        'Accept-Language': getVrmAcceptLanguage(countryCode),
        'Accept': accept,
        'X-Originating-IP': xOriginatingIp
    }

    returnVal = {
        'error' : 'Test Error'
    }

    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Retrieve {countryCodes[countryCode]} Vehicle Details Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'vehicles': responseData['data']}
        else:
            print(f"Retrieve {countryCodes[countryCode]} Vehicle Details error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Retrieve {countryCodes[countryCode]} Vehicle Details error details : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

# MID by Engine Code

def retrieve_MID_by_Engine_Code(engine_code: str, method: str = 'engine_code', country_code: str = 'gb', page: int = 1, limit: int = 20):
    url = getMidByEngineCode(engine_code=engine_code, method=method, countryCode=country_code, page=page, limit=limit)
    print(f"Retrieve MID by Engine Code url : {url}")

    headers = {
        'Accept-Language': getVrmAcceptLanguage(country_code),
        'Accept': accept,
        'X-Originating-IP': xOriginatingIp
    }

    returnVal = {
        'error' : 'Test Error'
    }

    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Retrieve MID by Engine Code Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'vehicles': responseData['data']}
        else:
            print(f"Retrieve MID by Engine Code error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Retrieve MID by Engine Code error details : {e}")
        returnVal['error'] = f'{e}'
    return returnVal