from constants import *

# V1 Urls

## Vehicle Selection

class V1Urls:

    def searchMfdByCountry(page:int = 1, limit:int = 10, countryCode:str = 'gb'):
        url = f'{AUTODATA_BASE_URL}v1/manufacturers?country-code={countryCode}&'
        if(page>0):
            url = f'{url}page={page}&'
        if(limit>0):
            url = f'{url}limit={limit}&'
        return f'{url}api_key={AUTODATA_API_KEY}'

    def searchModelByMfdId(mfd_id:str = '', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/manufacturers/{mfd_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getVehicleByMfgAndModel(mfd_id:str = 'AUD0', model_id:str = '7000022', page:int = 1, limit:int = 10, countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles?manufacturer_id={mfd_id}&model_id={model_id}&country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'

    def getVehiclesByMid(mid:str = 'AUD16232', links:str = 'no', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}?links={links}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Service Schedules

    def searchServiceScheduleId(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-schedules?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getServiceScheduleIntervalsData(mid:str = 'AUD00528', ss_id:str = 'AUDSG0000082', parts:str = 'no', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-schedules/{ss_id}?parts={parts}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getServiceScheduleIntervalOperationsData(mid:str = 'AUD00528', ss_id:str = 'AUDSG0000082', int_id:str = '1', parts:str = 'no', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-schedules/{ss_id}/intervals/{int_id}?parts={parts}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def searchCamshiftDriveReplacementIntervalId(mid:str = 'AUD00528', page:int = 1, limit:int = 10, countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/replacement-times?country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'

    def getCamshiftDriveReplacementIntervalData(mid:str = 'AUD00528', rep_time_id: str = 'AUDSG0000032', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/replacement-times/{rep_time_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Lifetime Summary

    def searchLifetimeSummaryId(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/lifetime-summary?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getLifetimeSummaryData(mid:str = 'AUD00528', sum_id:str = 'AUDSG0000082', parts:str = 'no', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/lifetime-summary/{sum_id}?parts={parts}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Service Advisor

    def searchServiceAdvisorScheduleId(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-advisor?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getServiceAdvisorScheduleData(mid:str = 'AUD00528', ss_id:str = 'AUDSG0000082', parts:str = 'no', miles:str = '250000', kms:str = '250000', months:int = 26, countryCode:str = 'gb'):
        url = f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-advisor/{ss_id}?parts=no&miles=25000&months=26&country-code=gb&api_key={AUTODATA_API_KEY}'
        if(parts.strip()!=''):
            url = f'{url}parts={parts}&'
        if(miles.strip()!=''):
            url = f'{url}miles={miles}&'
        if(kms.strip()!=''):
            url = f'{url}kilometers={kms}&'
        if(months>0):
            url = f'{url}months={months}&'
        url = f'{url}country-code={countryCode}&api_key={AUTODATA_API_KEY}'
        return url

    ## Common Service Operations

    def commonServiceOpsData(mid:str = 'AUD00528', ss_id:str = 'AUDSG0000082', operation_id:str = '', renew_check:int = 1, miles:str = '250000', kms:str = '250000', months:int = 12, countryCode:str = 'gb'):
        '''
        renew_check: **Renew(1)** or **Check(2)**
        '''
        url = f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/common-service-operations/{ss_id}?'
        if(operation_id.strip()!=''):
            url = f'{url}operation_id={operation_id}&'
        if(renew_check.strip()!=''):
            url = f'{url}renew_check={renew_check}&'
        if(miles.strip()!=''):
            url = f'{url}service_miles={miles}&'
        if(kms.strip()!=''):
            url = f'{url}service_kms={kms}&'
        if(months>0):
            url = f'{url}service_months={months}&'
        url = f'{url}country-code={countryCode}&api_key={AUTODATA_API_KEY}'
        return url

    ## Service Indicators

    def searchServiceIndicatorId(mid:str = 'BMW30146', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-indicators?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getServiceIndicatorData(mid:str = 'BMW30146', service_indicator_id:str = 'bmw17ser', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/service-indicators/{service_indicator_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Service Brakes

    def getServiceBrakeData(mid:str = 'BMW00712', location:str = '', countryCode:str = 'gb'):
        '''
        location : **front** or **rear**
        '''
        if(location.strip()==''):
            return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/brakes-check-and-replace?country-code={countryCode}&api_key={AUTODATA_API_KEY}'
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/brakes-check-and-replace?location={location}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Engine Oil

    def getEngineOilData(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/engine-oils?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Transmissions

    def searchTransmissionId(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/transmissions?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getTransmissionsData(mid:str = 'AUD00528', ops_id:str = 'manual-transmission', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/transmissions/{ops_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Operation Lifetime Summary

    def searchOpsLifeSumId(mid:str = '', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/operation-lifetime-summary?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getOpsLifeSumData(mid:str = 'AUD13071', ss_id:str = 'AUDSG0000025', ops_id:str = '13,17,18', ops_type:str = '', parts:str = 'no', miles:str = '40000', kms:str = '25000', months:int = 36, countryCode:str = 'gb'):
        if(ops_id==''):
            return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/operation-lifetime-summary/{ss_id}?operation_id={ops_id}&parts={parts}&service_miles={miles}&service_kms={kms}&service_months={months}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/operation-lifetime-summary/{ss_id}?operation_id={ops_id}&operation_type_id={ops_type}&parts={parts}&service_miles={miles}&service_kms={kms}&service_months={months}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Key Operation Lifetime Summary

    def searchKeyOpsLifeSumId(mid:str = 'AUD13071', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/key-operation-lifetime-summary?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getKeyOpsLifeSumData(mid:str = 'AUD13071', ss_id:str = 'AUDSG0000025', ops_id:str = '13,17,18', ops_type:str = '', parts:str = 'no', miles:str = '40000', kms:str = '25000', months:int = 36, countryCode:str = 'gb'):
        if(ops_id==''):
            return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/key-operation-lifetime-summary/{ss_id}?operation_id={ops_id}&parts={parts}&service_miles={miles}&service_kms={kms}&service_months={months}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/key-operation-lifetime-summary/{ss_id}?operation_id={ops_id}&operation_type_id={ops_type}&parts={parts}&service_miles={miles}&service_kms={kms}&service_months={months}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## AC Recharge

    def getAcRechargeData(mid:str = 'BMW00785', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/ac-recharge-information?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Engine Management

    def searchEngineManagementId(mid:str = 'AUD13073', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/engine-management?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getEngineManagementData(mid:str = 'AUD13073', em_id:str = 'aud17em', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/engine-management/{em_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Camshaft Drive Replacements

    def searchCamShiftId(mid:str = 'NIS14555', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/camshaft-drive-replacements?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getCamShiftData(mid:str = 'NIS14555', cd_id:str = 'nis18gr', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/camshaft-drive-replacements/{cd_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Auxiliary Drive Belts

    def searchAuxBeltId(mid:str = 'FOR12298', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/auxiliary-drive-belts?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getAuxBeltData(mid:str = 'FOR12298', ad_id:str = 'for26adb', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/auxiliary-drive-belts/{ad_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Clutch Replacements

    def searchClutchReplacementIds(mid: str = 'FIA24734', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/clutch-replacements?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getClutchReplacementData(mid: str = 'FIA24734', cr_id:str = 'fia35clutch', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/clutch-replacements/{cr_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Degat Procedures

    def searchDegatProcedureId(mid:str = 'ALF16102', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/degat-procedures?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getDegatProcedureData(mid:str = 'ALF16102', deg_id:str = 'alf2degat', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/degat-procedures/{deg_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## ABS

    def searchAbsId(mid:str = 'MER27109', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/abs?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getAbsData(mid:str = 'MER27109', v_id:str = 'mer10abs', drive:str = 'LH', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/abs/{v_id}?drive={drive}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Wheel Alignment Data

    def searchWheelAlignmentId(mid:str = 'MER27109', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/wheel-alignment-data?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getWheelAlignmentData(mid:str = 'MER27109', v_id:str = '34949', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/wheel-alignment-data/{v_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Wheel Alignment Data Notes

    def getWheelAlignmentNotes(mid:str = 'MER27109', var_id:str = '34949', note_id:str = '90', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/wheel-alignment-data/{var_id}/notes/{note_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Tyre Pressure

    def searchTyrePressureId(mid:str = 'AUD14115', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tyre-pressures?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getTyrePressureData(mid:str = 'AUD14115', tp_id:str = '28286', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tyre-pressures/{tp_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Electronic Parking Brakes

    def searchElParkBrakeId(mid:str = 'JAG13276', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/electronic-parking-brake?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getElParkBrakeData(mid:str = 'JAG13276', elParkId:str = 'jag3epb', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/electronic-parking-brake/{elParkId}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## TPMS

    def searchTpmsId(mid:str = 'CIT18229', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tpms?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getTpmsData(mid:str = 'CIT18229', tpms_id:str = 'cit5tpms', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tpms/{tpms_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Tyres

    def getTyresData(mid:str = 'FIA25296', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tyres?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## SRS

    def searchSrsId(mid:str = 'ALF00051', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/airbags-data/?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getSrsData(mid:str = 'ALF00051', srsid:str = 'alf6srs', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/airbags-data/{srsid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def searchSrsSelfDiagId(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/airbags-self-diagnosis/?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getSrsSelfDiagData(mid:str = 'AUD00528', srsid:str = 'aud1srssd', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/airbags-self-diagnosis/{srsid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Key Programming Procedures

    def searchKeyProgrammingId(mid:str = 'MAZ18160', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/key-programming-procedures?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getKeyProgrammingData(mid:str = 'MAZ18160', kid:str = 'maz16imm', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/key-programming-procedures/{kid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Battery Replacements

    def searchBatteryReplacementId(mid:str = 'MAZ29725', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/battery-replacements?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getBatteryReplacementData(mid:str = 'MAZ29725', bat_rep_id:str = 'maz17erbd', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/battery-replacements/{bat_rep_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Wiring Diagrams

    def getWiringDiagramsData(mid:str = 'FOR01887', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/wiring-diagrams?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Diagnostic Trouble Codes

    def getDtcData(mid:str = 'AUD00528', dtc:str = '00268', system:str = '', countryCode:str = 'gb'):
        '''
        Expected System values : ABS, AC, SRS, EM, IMMOB, TRAN, or EOBD
        '''
        if(system.strip()==''):
            return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/dtc/{dtc}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/dtc/{dtc}?system={system}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getPartsDataByWiringComponent(wd_id:str = 'A005', method:str = 'ad_components_id', countryCode:str = 'gb', page:int = 1, limit:int = 20):
        return f'{AUTODATA_BASE_URL}/v1/parts?id={wd_id}&method={method}&country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'

    ## Electrical Components Locations

    def searchElectricalComponentLocationId(mid:str = 'VOL05471', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/electrical-components-locations?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getElectricalComponentLocationData(mid:str = 'VOL05471', el_loc_id:str = 'vol1ecl', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/electrical-components-locations/{el_loc_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Bulb Replacements

    def getBulbReplacementData(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/bulbs?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Injection and Ignition

    def getInjectionAndIgnitionData(mid:str = 'PEU16464', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/injection-and-ignition-systems?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Tuning and Emissions

    def getTuningAndEmissionsData(mid:str = 'BMW00785', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tuning-and-emissions?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Emissions

    def searchEmissionData(mid:str = 'POR04126', countryCode:str = 'de'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/emissions?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getEmissionData(mid:str = 'POR04126', eid:str = '01063001', countryCode:str = 'de'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/emissions/{eid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Engine Service Operations

    def getEngineServiceOperationsData(mid:str = 'BMW00785', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/engine-service-operations?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Lubricants and Fluids

    def searchLubricantAndFluidId(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/lubricants?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getLubricantAndFluidDataForSpecificSystem(mid:str = 'AUD00528', option_id:str = 'brakes', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/lubricants/{option_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getAllLubricantAndFluidData(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/all-lubricants?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Tightening Torques

    def getTighteningTorquesData(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tightening-torques?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Starting and Charging

    def getStartingAndChargingData(mid:str = 'PEU06540', countryCode:str = 'PEU06540'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/starting-and-charging?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Brake Disc and Drum Dimensions

    def getBrakeDiscAndDrumDimensions(mid:str = 'AUD13073', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/brake-disc-and-drum-dimensions?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Repair Times

    def searchRepairTimeIds(mid:str = 'AUD00528', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getRepairTimeForOperations(mid:str = 'AUD00528', r_t_id:str = '49551', parts:str = 'no', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times/{r_t_id}?parts={parts}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getRepairTimeForSpecificOperation(mid:str = 'AUD00528', r_t_id:str = '49551', rid:str = 'A2.0700', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times/{r_t_id}/repairs/{rid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Repair Times by Genartnr

    def searchRepairTimeByGenartnr(mid:str = 'PEU14794', rid:str = '01525', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times-by-genartnrs/{rid}/variants?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getRepairTimeByGenartnr(mid:str = 'PEU14794', rid:str = '01525', vid:str = '51303', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times-by-genartnrs/{rid}/variants/{vid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Repair Times by Ad Part

    def searchRepairTimeByAd(mid:str = 'PEU14794', part_id:str = '000275', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times-by-ad-parts/{part_id}/variants?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getRepairTimeByAd(mid:str = 'PEU14794', rid:str = '000275', vid:str = '51303', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repair-times-by-ad-parts/{rid}/variants/{vid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Repair Times by ID Voce

    def getRepairTimeByIdVoce(mid:str = 'VOL16198', rid:str = '3086', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/docs/v1/vehicles/{mid}/repair-times-by-idvoce/{rid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Repair Times for all Vehicle Variants

    def searchVehicleVariantRepairTimeId(mid:str = 'BMW00597', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repairs?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getVehicleVariantRepairTimeData(mid:str = 'BMW00597', rid:str = '10', parts:str = 'no', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/repairs/{rid}?parts={parts}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Known Problem Areas

    def searchProblemAreaId(mid:str = 'ALF00176', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/known-problem-areas?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getProblemAreaData(mid:str = 'ALF00176', kid:str = 'body', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/known-problem-areas/{kid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Fixes and Bulletins

    def searchFixId(mid:str = 'ALF00176', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/fixes-and-bulletins?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getFixData(mid:str = 'ALF00176', fid:str = 'alf4fix', countryCode:str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/fixes-and-bulletins/{fid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Notes

    def getNotesData(mid: str = 'BMW00677', noteId: str = '296', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/notes/{noteId}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Tools

    def getToolsData(mid: str = 'BMW00677', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/tools?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Components Locations

    def searchComponentLocationData(mid: str = 'VOL05471', countryCode: str = 'gb', drive: str = 'RH'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/components-locations?country-code={countryCode}&drive={drive}&api_key={AUTODATA_API_KEY}'

    def getComponentLocationData(mid: str = 'VOL05471', c_locations: str = 'vol8ac', subject: str = 'air-conditioning', drive: str = 'RH', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/components-locations/{c_locations}?subject={subject}&drive={subject}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Jacking Procedures

    def searchJackingProcedureData(mid: str = 'CIT12849', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/jacking-procedures?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getJackingProcedureData(mid: str = 'CIT12849', procedure_id: str = 'cit5jp', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/jacking-procedures/{procedure_id}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Glass Cars

    def getGlassCarImageData(mid: str = 'AUD30267', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/glass-cars?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Illustrations

    def getIllustrationsData(subject: str = 'SILL-PF', mid: str = 'MER13470', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/illustrations/image-subjects/{subject}/images?mid={mid}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Images

    def getImageData(mid: str = 'FOR13463', image_id: str = '117177-for24abs', subject: str = 'abs', drive: str = 'LH', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/images/{image_id}?subject={subject}&drive={drive}&country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Operation Types ID

    def searchAllOperationTypesId(countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/operation-types?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Operation ID

    def searchAllOperationId(countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/operations?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## Symbols

    def searchAllSymbols(countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/symbols?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    ## PIN Data

    def searchPinId(mid: str = 'FOR13249', countryCode: str = 'gb'):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/pin-data?country-code={countryCode}&api_key={AUTODATA_API_KEY}'

    def getPinData(mid: str = 'FOR13249', pid: str = 'for6pod', countryCode: str = 'gb', ):
        return f'{AUTODATA_BASE_URL}/v1/vehicles/{mid}/pin-data/{pid}?country-code={countryCode}&api_key={AUTODATA_API_KEY}'