from constants import *
import controllers as ctr

def getIpInfoUrl(ipAddress: str):
    url = f'https://ipinfo.io/{ipAddress}/json?token={IPINFO_API_KEY}'
    return url

# V2 Url Controllers

def getVehicleVrmSearchUrl(vrmId: str, callType: str = 'vrm', countryCode: str = 'se', page: int = 1, limit: int = '20'):
    url = f'{AUTODATA_BASE_URL}/v1/vehicle-identifiers/{ctr.getVrmPlatesPath(countryCode)}/{vrmId}?country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'
    if(countryCode in ('ir', 'du')):
        url = f'{AUTODATA_BASE_URL}/v1/vehicles?id={vrmId}&method={ctr.getVrmPlatesPath(countryCode, callType)}&country-code=gb&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'
    return url

def getMidByKtype(Ktype: str, method: str = 'k_type', countryCode: str = 'gb', page: int = 1, limit: int = 20):
    url = f'{AUTODATA_BASE_URL}/v1/vehicles?id={Ktype}&method={method}&country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'
    return url

def getKtypeByMid(mid: str, method: str = 'k_type', countryCode: str = 'gb', page: int = 1, limit: int = 20):
    url = f'{AUTODATA_BASE_URL}/v1/external-ids?mid={mid}&method={method}&country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'
    return url

def getVehicleByOtherCountryId(vehicle_id: str, method: str = 'kba', countryCode: str = 'gb', page: int = 1, limit: int = 20):
    url = f'{AUTODATA_BASE_URL}/v1/vehicles?id={vehicle_id}&method={method}&country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'
    return url

def getMidByEngineCode(engine_code: str, method: str = 'engine_code', countryCode: str = 'gb', page: int = 1, limit: int = 20):
    url = f'{AUTODATA_BASE_URL}/v1/vehicles?id={engine_code}&method={method}&country-code={countryCode}&page={page}&limit={limit}&api_key={AUTODATA_API_KEY}'
    return url