import json
import requests
import urls.v2_urls as v2_urls

# V2 Call Controllers

def getCountryFromIp(ipAddress: str):
    url = v2_urls.getIpInfoUrl(ipAddress=ipAddress)

    print(f"Country Fetching url : {url}")
    returnVal = 'gb'
    try:
        response = requests.get(url=url)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['country'] is not None):
                returnVal = str(responseData['country']).lower()
        else:
            print(f"Country fetching error code : {response.status_code} : {response}")
    except Exception as e:
        print(f"Country fetching error details : {e}")
        return 'error'
    return returnVal

def getVrmPlatesPath(countryCode:str = 'se', callType: str='vrm'):
    '''
    All Country Codes : [se, gb, fr, fi, ir, du]
    '''
    if(callType=='vrm'):
        if(countryCode=='se'):              # Sweden
            return 'se-plates'
        elif(countryCode=='gb'):            # Great Britain/United Kingdom
            return 'uk-plates'
        elif(countryCode=='fr'):            # France
            return 'fr-plates'
        elif(countryCode=='fi'):            # Finland
            return 'fi-plates'
        elif(countryCode=='ir'):            # Ireland
            return 'ie_vrm'
        elif(countryCode=='du'):            # Dutch
            return 'nl_vrm'
    else:
        if(countryCode=='se'):              # Sweden
            return 'se-plates'
        elif(countryCode=='gb'):            # Great Britain/United Kingdom
            return 'uk-plates'
        elif(countryCode=='fr'):            # France
            return 'fr-plates'
        elif(countryCode=='fi'):            # Finland
            return 'fi-plates'
        elif(countryCode=='ir'):            # Ireland
            return 'ie_vrm'
        elif(countryCode=='du'):            # Dutch
            return 'nl_vrm'
    return 'se-plates'

def getVrmAcceptLanguage(countryCode:str = 'se'):
    '''
    All Country Codes : [se, gb, fr, fi, ir, du]
    '''
    if(countryCode=='se'):              # Sweden
        return 'sv-se;q=0.8,en;q=0.7,fr-fr;q=0.4'
    elif(countryCode=='gb'):            # Great Britain/United Kingdom
        return 'en-gb;q=0.8,en;q=0.7,fr-fr;q=0.4'
    elif(countryCode=='fr'):            # France
        return 'fr-fr;q=0.8,en;q=0.7,fr-fr;q=0.4'
    elif(countryCode=='fi'):            # Finland
        return 'fi-fi;q=0.8,en;q=0.7,fr-fr;q=0.4'
    elif(countryCode=='ir'):            # Ireland
        return 'en-gb;q=0.8,en;q=0.7,fr-fr;q=0.4'
    elif(countryCode=='du'):            # Dutch
        return 'en-gb;q=0.8,en;q=0.7,fr-fr;q=0.4'
    elif(countryCode=='ch'):            # Switzerland
        return 'de-ch'
    return 'sv-se;q=0.8,en;q=0.7,fr-fr;q=0.4'

def getMonthAsInt(month:str = 'January'):
    if(month.isdigit()):
        return int(month)
    month = month.lower().strip()
    months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december']
    monthsMin = ['jan', 'feb', 'mar', 'april', 'may', 'june', 'july', 'aug', 'sept', 'oct', 'nov', 'dec']
    if month in months:
        index = months.index(month)
        return index+1
    if month in monthsMin:
        index = monthsMin.index(month)
        return index+1
    return 0