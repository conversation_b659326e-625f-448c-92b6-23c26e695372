# carmy-api

## Running the Backend

To Run the backend API-EndPoint, use the below code : 
`python endpoint.py`

Additionally, I've added an untested script [`carmy-endpoint`](./carmy-endpoint.sh) which installs all required additional directories based on the OS and runs the endpoint without having to use a code to run it. This assumes that you already have python installed on the system and can use pip to install libraries.

After running the above code, you will get a local base_url value native to your device. To access the swagger page, please use the path `base_url/docs`.
Alternatively, you may also be able to access it through the url [`https://localhost:5000/docs`](https://localhost:5000/docs) but this might not work on every device.