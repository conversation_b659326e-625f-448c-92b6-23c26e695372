origins = ['*']

methods = ['*']

headers = ['*']

allow_credentials = True

api_port = 5000
ipAddress = "0.0.0.0"
worker_count = 1
reloadFlag = True

assetImages = './images'

# IPINFO_API_KEY = 'a337f273cc570f'     # Personal
IPINFO_API_KEY = '1977dc86293097'       # Carmy

AUTODATA_API_KEY = '7jcvtj6s57wxn87ab4a7sez3'

AUTODATA_BASE_URL = 'https://api.autodata-group.com/docs'

accept = 'application/json'
xOriginatingIp = '***************'

countryCodes = {
    'se': "Swedish",
    'gb': 'British',
    'fr': 'French',
    'fi': 'Finnish',
    'ir': 'Irish',
    'du': 'Dutch',
    'ch': 'Swiss'
}
