import json
import requests
from classes import *
from constants import *
from controllers import *
from urls.v1_urls import V1Urls

headers = {
    'Accept': accept,
    'X-Originating-IP': xOriginatingIp
}

# V1 Call Controllers

## Vehicle Selection

def searchMfdByCountry(countryCode:str, pageSpecs:PageSpecs):
    url = V1Urls.searchMfdByCountry(page=pageSpecs.page, limit=pageSpecs.limit, countryCode=countryCode)
    print(f'Search MFD By Country URL : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
        else:
            print(f"Searching MFD by Country error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching MFD by Country Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def searchModelsByMfd(mfdId:str, countryCode:str):
    url = V1Urls.searchModelByMfdId(mfd_id=mfdId, countryCode=countryCode)
    print(f'Search Models by MFD : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
        else:
            print(f"Searching Model by MFD error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Model by MFD Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def getVehicleByMfdAndModel(mfdId:str, modelId:str, countryCode:str, pageSpecs:PageSpecs):
    url = V1Urls.getVehicleByMfgAndModel(mfd_id=mfdId, model_id=modelId, page=pageSpecs.page, limit=pageSpecs.limit, countryCode=countryCode)
    print(f'Search Vehicle by MFD and Model : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
        else:
            print(f"Searching Vehicle by MFD and Model error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Model by MFD and Model Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def getVehicleMyMid(mid:str, links:str, countryCode:str):
    url = V1Urls.getVehiclesByMid(mid=mid, links=links, countryCode=countryCode)
    print(f'Search Vehicle by MID : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
        else:
            print(f"Searching Vehicle by MID error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Model by MID Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Service Schedules

def searchServiceScheduleId(mid:str, countryCode:str):
    url = V1Urls.searchServiceScheduleId(mid=mid, countryCode=countryCode)
    print(f'Search Service Schedule Id : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
        else:
            print(f"Searching Service Schedule Id error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Schedule Id Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Lifetime Summary

def searchLifetimeSummaryId(mid:str, countryCode:str):
    url = V1Urls.searchLifetimeSummaryId(mid=mid, countryCode=countryCode)
    print(f'Search Lifetime Summary Id : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Lifetime Summary Id error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Lifetime Summary Id Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def getLifetimeSummaryData(mid:str, sumId:str, parts:str, countryCode:str):
    url = V1Urls.getLifetimeSummaryData(mid=mid, countryCode=countryCode, sum_id=sumId, parts=parts)
    print(f'Search Lifetime Summary Data : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Lifetime Summary Data error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Lifetime Summary Data Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Service Advisor

def searchServiceAdvisorId(mid:str, countryCode:str):
    url = V1Urls.searchServiceAdvisorScheduleId(mid=mid, countryCode=countryCode)
    print(f'Search Service Advisor Id Url : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Service Advisor ID error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Advisor ID Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def getSearchAdvisorData(mid:str, ssId:str, countryCode:str, parts:str, kms:str, miles:str, months:str):
    url = V1Urls.getServiceAdvisorScheduleData(
        mid=mid, 
        countryCode=countryCode, 
        ss_id=ssId, 
        parts=parts, 
        kms=kms, 
        miles=miles, 
        months=getMonthAsInt(months)
        )
    print(f'Search Service Advisor Data Url : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Service Advisor Data error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Advisor Data Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Common Service Operations

def getCommonServiceIntervals(mid:str, ssId:str, opsId:str, renewCheck:str, countryCode:str, miles:str, kms:str, months:str):
    url = V1Urls.commonServiceOpsData(
        mid=mid, 
        countryCode=countryCode, 
        ss_id=ssId, 
        kms=kms, 
        miles=miles, 
        months=getMonthAsInt(months), 
        renew_check=int(renewCheck), 
        operation_id=opsId
        )
    print(f'Search Service Advisor Data Url : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Service Advisor Data error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Advisor Data Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Service Indicators

def searchServiceIndicatorId(mid:str, countryCode:str):
    url = V1Urls.searchServiceIndicatorId(
        mid=mid, 
        countryCode=countryCode
        )
    print(f'Search Service Indicator Id Url : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Service Indicator Id error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Indicator Id Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

def getServiceIndicatorData(mid:str, serviceIndicatorId:str, countryCode:str):
    url = V1Urls.getServiceIndicatorData(
        mid=mid, 
        service_indicator_id=serviceIndicatorId,
        countryCode=countryCode
        )
    print(f'Search Service Indicator Data Url : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Service Indicator Data error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Indicator Data Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Service Brakes

def getServiceBrakeData(mid:str, location:str, countryCode:str):
    url = V1Urls.getServiceBrakeData(
        mid=mid, 
        location=location,
        countryCode=countryCode
        )
    print(f'Search Service Brake Data Url : {url}')
    headers['Accept-Language'] = getVrmAcceptLanguage(countryCode)
    returnVal = {
        'error' : 'Call Initialization Error'
    }
    try:
        response = requests.get(url=url, headers=headers)
        if(response.status_code==200):
            responseData = response.json()
            print(f"Response : {json.dumps(responseData, indent=4)}")
            if(responseData['data'] is not None):
                returnVal = {'data': responseData['data']}
            if(responseData['metadata'] is not None):
                returnVal = {'metadata': responseData['metadata']}
        else:
            print(f"Searching Service Brake Data error code : {response.status_code} : {response}")
            returnVal['error'] = response.status_code
    except Exception as e:
        print(f"Searching Service Brake Data Error : {e}")
        returnVal['error'] = f'{e}'
    return returnVal

## Engine Oil

## Transmissions

## Operation Lifetime Summary

## Key Operation Lifetime Summary

## AC Recharge

## Engine Management

## Camshaft Drive Replacements

## Auxiliary Drive Belts

## Clutch Replacements

## Degat Procedures

## ABS

## Wheel Alignment Data

## Wheel Alignment Data Notes

## Tyre Pressure

## Electronic Parking Brakes

## TPMS

## Tyres

## SRS

## Key Programming Procedures

## Battery Replacements

## Wiring Diagrams

## Diagnostic Trouble Codes

## Electrical Components Locations

## Bulb Replacements

## Injection and Ignition

## Tuning and Emissions

## Emissions

## Engine Service Operations

## Lubricants and Fluids

## Tightening Torques

## Starting and Charging

## Brake Disc and Drum Dimensions

## Repair Times

## Repair Times by Genartnr

## Repair Times by Ad Part

## Repair Times by ID Voce

## Repair Times for all Vehicle Variants

## Known Problem Areas

## Fixes and Bulletins

## Notes

## Tools

## Components Locations

## Jacking Procedures

## Glass Cars

## Illustrations

## Images

## Operation Types ID

## Operation ID

## Symbols

## PIN Data