#!/bin/bash

# Detect the OS
ios="$(uname -s)"

# Install dependencies from requirements.txt if it exists
if [[ -f "requirements.txt" ]]; then
    if [[ "$ios" == "Linux" || "$ios" == "Darwin" ]]; then
        echo "Installing dependencies on Linux/macOS"
        python3 -m pip install --upgrade pip
        python3 -m pip install -r requirements.txt
    elif [[ "$ios" =~ MINGW|CYGWIN|MSYS ]]; then
        echo "Installing dependencies on Windows"
        python.exe -m pip install --upgrade pip
        python.exe -m pip install -r requirements.txt
    else
        echo "Unknown OS: $ios"
        exit 1
    fi
else
    echo "requirements.txt not found. Skipping dependency installation."
fi

if [[ "$ios" == "Linux" ]]; then
    echo "Running on Linux"
    python3 endpoint.py
elif [[ "$ios" == "Darwin" ]]; then
    echo "Running on macOS"
    python3 endpoint.py
elif [[ "$ios" =~ MINGW|CYGWIN|MSYS ]]; then
    echo "Running on Windows"
    python.exe endpoint.py
else
    echo "Unknown OS: $ios"
    exit 1
fi